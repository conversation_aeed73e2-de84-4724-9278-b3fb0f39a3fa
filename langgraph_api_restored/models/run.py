# Source Generated with <PERSON>om<PERSON>le++
# File: run.pyc (Python 3.12)

import asyncio
import contextlib
import time
import urllib.parse as urllib
import uuid
from collections.abc import Mapping, Sequence
from typing import Any, NamedTuple, cast
from uuid import UUID
import or<PERSON><PERSON>
import structlog
from starlette.authentication import BaseUser
from starlette.exceptions import HTTPException
from typing_extensions import TypedDict
from langgraph_api.graph import GRAPHS, get_assistant_id
from langgraph_api.schema import All, Config, Context, IfNotExists, MetadataInput, MultitaskStrategy, OnCompletion, Run, RunCommand, StreamMode
from langgraph_api.utils import AsyncConnectionProto, get_auth_ctx
from langgraph_api.utils.headers import should_include_header
from langgraph_api.utils.uuids import uuid7
from langgraph_runtime.ops import Runs
logger = structlog.stdlib.get_logger(__name__)

def LangSmithTracer():
    '''LangSmithTracer'''
    project_name: str | None = 'Configuration for LangSmith tracing.'

LangSmithTracer = LangSmithTracer = TypedDict(\'LangSmithTracer\', {}, total=False)

class RunCreateDict(TypedDict):
    langsmith_tracer: LangSmithTracer | None = 'Payload for creating a run.'


def ensure_ids(assistant_id = None, thread_id = None, payload = None):
    results = [
        assistant_id if isinstance(assistant_id, UUID) else UUID(assistant_id)]
    if thread_id:
        results.append(thread_id if isinstance(thread_id, UUID) else UUID(thread_id))
    else:
        results.append(None)
    checkpoint_id = payload.get('checkpoint_id')
    if payload.get('checkpoint_id'):
        results.append(checkpoint_id if isinstance(checkpoint_id, UUID) else UUID(checkpoint_id))
        return tuple(results)
    None.append(None)
    return tuple(results)
# WARNING: Decompyle incomplete


def assign_defaults(payload = None):
    if payload.get('stream_mode'):
        stream_mode = payload['stream_mode'] if isinstance(payload['stream_mode'], list) else [
            payload['stream_mode']]
    else:
        stream_mode = [
            'values']
    if not payload.get('multitask_strategy'):
        payload.get('multitask_strategy')
    multitask_strategy = 'enqueue'
    prevent_insert_if_inflight = multitask_strategy == 'reject'
    return (stream_mode, multitask_strategy, prevent_insert_if_inflight)


def get_user_id(user = None):
    pass
# WARNING: Decompyle incomplete

LANGSMITH_METADATA = 'langsmith-metadata'
LANGSMITH_TAGS = 'langsmith-tags'
LANGSMITH_PROJECT = 'langsmith-project'
DEFAULT_RUN_HEADERS_EXCLUDE = {
    'x-api-key',
    'x-tenant-id',
    'x-service-key'}

def get_configurable_headers(headers = None):
    '''Extract headers that should be added to run configuration.

    This function handles special cases like langsmith-trace and baggage headers,
    while respecting the configurable header patterns.
    '''
    configurable = { }
    for key, value in headers.items():
        if key == 'langsmith-trace':
            configurable[key] = value
            baggage = headers.get('baggage')
            if headers.get('baggage'):
                for item in baggage.split(','):
                    (baggage_key, baggage_value) = item.split('=')
                    if baggage_key == LANGSMITH_METADATA and baggage_key not in configurable:
                        configurable[baggage_key] = orjson.loads(urllib.parse.unquote(baggage_value))
                        continue
                    if baggage_key == LANGSMITH_TAGS:
                        configurable[baggage_key] = urllib.parse.unquote(baggage_value).split(',')
                        continue
                    if not baggage_key == LANGSMITH_PROJECT:
                        continue
                    configurable[baggage_key] = urllib.parse.unquote(baggage_value)
            continue
        if key.startswith('x-'):
            if key in DEFAULT_RUN_HEADERS_EXCLUDE:
                if should_include_header(key):
                    configurable[key] = value
                continue
            if not should_include_header(key):
                continue
            configurable[key] = value
            continue
        if key == 'user-agent':
            if not should_include_header(key):
                continue
            configurable[key] = value
            continue
        if not should_include_header(key):
            continue
        configurable[key] = value
    return configurable


async def create_valid_run(conn, thread_id, payload, headers = None, barrier = None, run_id = None, request_start_time = (None, None, None, False), temporary = ('conn', AsyncConnectionProto, 'thread_id', str | None, 'payload', RunCreateDict, 'headers', Mapping[(str, str)], 'barrier', asyncio.Barrier | None, 'run_id', UUID | None, 'request_start_time', float | None, 'temporary', bool, 'return', Run)):
    pass
# WARNING: Decompyle incomplete


class _Ids(NamedTuple):
    run_id: uuid.UUID = '_Ids'


def _get_ids(thread_id = None, payload = None, run_id = None):
    assistant_id = get_assistant_id(payload['assistant_id'])
    (assistant_id, thread_id_, checkpoint_id) = ensure_ids(assistant_id, thread_id, payload)
    if not run_id:
        run_id
    run_id = uuid7()
    return _Ids(assistant_id, thread_id_, checkpoint_id, run_id)

