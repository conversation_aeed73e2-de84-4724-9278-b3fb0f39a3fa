# Source Generated with <PERSON><PERSON><PERSON><PERSON>++
# File: backend.pyc (Python 3.12)

from typing import NotRequired
from starlette.authentication import AuthCredentials, AuthenticationBackend, AuthenticationError, BaseUser
from starlette.requests import HTTPConnection
from typing_extensions import TypedDict
from langgraph_api.auth.langsmith.client import auth_client
from langgraph_api.auth.studio_user import Studio<PERSON>ser
from langgraph_api.config import LANGSMITH_AUTH_VERIFY_TENANT_ID, LANGSMITH_TENANT_ID

class AuthDict(TypedDict):
    user_email: NotRequired[str] = 'AuthDict'


class AuthCacheEntry(TypedDict):
    user: StudioUser = None  # AuthCacheEntry


class LangsmithAuthBackend(AuthenticationBackend):
    
    def __init__(self):
        LRUCache = LRUCache
        import langgraph_api.utils.cache
        self._cache = LRUCache[AuthCacheEntry](max_size = 1000, ttl = 60)

    
    def _get_cache_key(self, headers):
        '''Generate cache key from authentication headers'''
        relevant_headers = (lambda .0: pass# WARNING: Decompyle incomplete
)(headers())
        return str(hash(relevant_headers))

    
    async def authenticate(self = None, conn = None):
        pass
    # WARNING: Decompyle incomplete


