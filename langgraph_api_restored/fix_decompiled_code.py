#!/usr/bin/env python3
"""
修复反编译代码中的常见问题
"""

import os
import re
from pathlib import Path

def fix_node_references(content):
    """修复 <NODE:xx> 引用"""
    # 替换常见的 NODE 引用
    patterns = [
        (r'<NODE:27>\(([^,]+), \'([^\']+)\', TypedDict, total = False\)', r'\1 = TypedDict(\'\2\', {}, total=False)'),
        (r'<NODE:(\d+)>', r'# FIXME: Unsupported node type \1'),
    ]
    
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)
    
    return content

def fix_import_statements(content):
    """修复导入语句"""
    # 修复一些常见的导入问题
    fixes = [
        ('import langgraph_api.patch as langgraph_api', 'import langgraph_api.patch'),
        ('from langgraph_runtime.lifespan import lifespan', '# from langgraph_runtime.lifespan import lifespan  # FIXME: Module not found'),
        ('from langgraph_runtime.retry import OVERLOADED_EXCEPTIONS', '# from langgraph_runtime.retry import OVERLOADED_EXCEPTIONS  # FIXME: Module not found'),
    ]
    
    for old, new in fixes:
        content = content.replace(old, new)
    
    return content

def fix_syntax_errors(content):
    """修复语法错误"""
    # 修复一些常见的语法问题
    fixes = [
        # 修复函数定义中的字符串赋值
        (r"(\w+): (\w+) \| None = '(\w+)'", r"\1: \2 | None = None  # \3"),
        (r"(\w+): (\w+) = '(\w+)'", r"\1: \2 = None  # \3"),
        # 修复不完整的条件语句
        (r'if not (.+) or (.+):', r'if not \1 or \2:'),
    ]
    
    for pattern, replacement in fixes:
        content = re.sub(pattern, replacement, content)
    
    return content

def process_file(file_path):
    """处理单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 应用修复
        content = fix_node_references(content)
        content = fix_import_statements(content)
        content = fix_syntax_errors(content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 修复文件: {file_path}")
            return True
        else:
            print(f"- 无需修复: {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ 处理文件失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("修复反编译代码中的常见问题")
    print("=" * 50)
    
    current_dir = Path('.')
    python_files = list(current_dir.rglob('*.py'))
    
    if not python_files:
        print("未找到 Python 文件")
        return
    
    fixed_count = 0
    total_count = len(python_files)
    
    for py_file in python_files:
        # 跳过这个修复脚本本身
        if py_file.name == 'fix_decompiled_code.py':
            continue
            
        if process_file(py_file):
            fixed_count += 1
    
    print(f"\n修复完成!")
    print(f"总文件数: {total_count}")
    print(f"修复文件数: {fixed_count}")
    print(f"无需修复: {total_count - fixed_count}")
    
    print("\n注意事项:")
    print("1. 某些复杂的语法错误可能需要手动修复")
    print("2. 请检查标记为 FIXME 的行")
    print("3. 建议使用 Python 语法检查工具验证代码")

if __name__ == "__main__":
    main()
