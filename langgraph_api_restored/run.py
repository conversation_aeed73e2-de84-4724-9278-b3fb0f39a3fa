#!/usr/bin/env python3
"""
LangGraph API 启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_packages = [
        'starlette',
        'uvicorn',
        'orjson',
        'structlog'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def setup_environment():
    """设置默认环境变量"""
    env_vars = {
        'STATS_INTERVAL_SECS': '60',
        'MIGRATIONS_PATH': '/storage/migrations',
        'POSTGRES_POOL_MAX_SIZE': '150',
        'RESUMABLE_STREAM_TTL_SECONDS': '120',
        'REDIS_MAX_CONNECTIONS': '2000',
        'REDIS_CONNECT_TIMEOUT': '10',
        'REDIS_MAX_IDLE_TIME': '120',
        'REDIS_STREAM_TIMEOUT': '30',
        'REDIS_KEY_PREFIX': '',
        'RUN_STATS_CACHE_SECONDS': '60',
        'ALLOW_PRIVATE_NETWORK': 'false',
        'CORS_ALLOW_ORIGINS': '*',
    }
    
    for key, default_value in env_vars.items():
        if key not in os.environ:
            os.environ[key] = default_value
            print(f"设置环境变量: {key}={default_value}")

def main():
    """主函数"""
    print("LangGraph API 启动脚本")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 设置环境变量
    setup_environment()
    
    # 检查关键环境变量
    if not os.getenv('DATABASE_URI') and not os.getenv('POSTGRES_URI'):
        print("警告: 未设置数据库连接 (DATABASE_URI 或 POSTGRES_URI)")
    
    if not os.getenv('REDIS_URI'):
        print("警告: 未设置 Redis 连接 (REDIS_URI)")
    
    # 启动服务器
    try:
        print("\n启动 LangGraph API 服务器...")
        print("访问地址: http://localhost:8000")
        print("按 Ctrl+C 停止服务器")
        
        # 尝试导入 server 模块
        try:
            import server
            if hasattr(server, 'app'):
                subprocess.run([
                    sys.executable, '-m', 'uvicorn', 
                    'server:app', 
                    '--host', '0.0.0.0', 
                    '--port', '8000',
                    '--reload'
                ])
            else:
                print("错误: server.py 中未找到 app 对象")
                sys.exit(1)
        except ImportError as e:
            print(f"错误: 无法导入 server 模块: {e}")
            print("请检查 server.py 文件是否存在且语法正确")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
