# Source Generated with <PERSON><PERSON><PERSON><PERSON>++
# File: queue_entrypoint.pyc (Python 3.12)

import os
disable_truststore = os.getenv('DISABLE_TRUSTSTORE')
if not os.getenv('DISABLE_TRUSTSTORE') or disable_truststore.lower() == 'true':
    import truststore
    truststore.inject_into_ssl()
import asyncio
import contextlib
import json
import logging.config as logging
import pathlib
import signal
from contextlib import asynccontextmanager
from typing import cast
import structlog
from langgraph_runtime.database import pool_stats
# from langgraph_runtime.lifespan import lifespan  # FIXME: Module not found
from langgraph_runtime.metrics import get_metrics
logger = structlog.stdlib.get_logger(__name__)

async def health_and_metrics_server():
    pass
# WARNING: Decompyle incomplete


async def entrypoint(grpc_port = None, entrypoint_name = None):
    pass
# WARNING: Decompyle incomplete


async def main(grpc_port = None, entrypoint_name = None):
    '''Run the queue entrypoint and shut down gracefully on SIGTERM/SIGINT.'''
    pass
# WARNING: Decompyle incomplete

# WARNING: Decompyle incomplete
