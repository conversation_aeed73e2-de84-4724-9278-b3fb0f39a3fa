# Source Generated with Decom<PERSON>le++
# File: schema.pyc (Python 3.12)

from typing import Any
from typing_extensions import TypedDict

class RequestPayload(TypedDict):
    data: dict = None  # RequestPayload


class ResponsePayload(TypedDict):
    data: Any | None = None  # ResponsePayload


class StreamPingData(TypedDict):
    id: str = None  # StreamPingData


class StreamData(TypedDict):
    value: Any = None  # StreamData


class ErrorData(TypedDict):
    message: str = None  # ErrorData

