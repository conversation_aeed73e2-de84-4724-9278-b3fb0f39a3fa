{"name": "console-table-printer", "version": "2.12.1", "repository": "github:ayonious/console-table-printer", "description": "Printing pretty tables on console log", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"setup": "yarn", "build": "tsc", "format": "prettier --write \"**/*.{json,md,ts,tsx,yml,js,jsx}\"", "test": "jest --config jestconfig.json", "lint": "eslint --ext=js,ts .", "semantic-release": "semantic-release"}, "keywords": ["console-table", "console-log", "print-table", "node-table-printing"], "files": ["dist"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@types/jest": "^29.5.12", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "eslint": "^9.5.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.0.11", "jest": "^29.7.0", "prettier": "^3.3.2", "pretty-quick": "^4.0.0", "semantic-release": "^24.0.0", "ts-jest": "^29.1.5", "typescript": "^5.4.5"}, "homepage": "https://console-table.netlify.app", "dependencies": {"simple-wcswidth": "^1.0.1"}}