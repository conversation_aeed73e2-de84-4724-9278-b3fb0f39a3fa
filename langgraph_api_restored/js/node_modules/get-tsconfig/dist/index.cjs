"use strict";var ve=Object.defineProperty;var l=(e,t)=>ve(e,"name",{value:t,configurable:!0});var a=require("node:path"),ee=require("node:fs"),Te=require("node:module"),Ae=require("resolve-pkg-maps"),Oe=require("fs");function B(e){return e.startsWith("\\\\?\\")?e:e.replace(/\\/g,"/")}l(B,"slash");const M=l(e=>{const t=ee[e];return(i,...n)=>{const s=`${e}:${n.join(":")}`;let o=i==null?void 0:i.get(s);return o===void 0&&(o=Reflect.apply(t,ee,n),i==null||i.set(s,o)),o}},"cacheFs"),F=M("existsSync"),je=M("readFileSync"),d=M("statSync"),ne=l((e,t,i)=>{for(;;){const n=a.posix.join(e,t);if(F(i,n))return n;const s=a.dirname(e);if(s===e)return;e=s}},"findUp"),R=/^\.{1,2}(\/.*)?$/,J=l(e=>{const t=B(e);return R.test(t)?t:`./${t}`},"normalizeRelativePath");function _e(e,t=!1){const i=e.length;let n=0,s="",o=0,r=16,c=0,u=0,p=0,T=0,w=0;function O(f,m){let g=0,y=0;for(;g<f||!m;){let j=e.charCodeAt(n);if(j>=48&&j<=57)y=y*16+j-48;else if(j>=65&&j<=70)y=y*16+j-65+10;else if(j>=97&&j<=102)y=y*16+j-97+10;else break;n++,g++}return g<f&&(y=-1),y}l(O,"scanHexDigits");function v(f){n=f,s="",o=0,r=16,w=0}l(v,"setPosition");function A(){let f=n;if(e.charCodeAt(n)===48)n++;else for(n++;n<e.length&&N(e.charCodeAt(n));)n++;if(n<e.length&&e.charCodeAt(n)===46)if(n++,n<e.length&&N(e.charCodeAt(n)))for(n++;n<e.length&&N(e.charCodeAt(n));)n++;else return w=3,e.substring(f,n);let m=n;if(n<e.length&&(e.charCodeAt(n)===69||e.charCodeAt(n)===101))if(n++,(n<e.length&&e.charCodeAt(n)===43||e.charCodeAt(n)===45)&&n++,n<e.length&&N(e.charCodeAt(n))){for(n++;n<e.length&&N(e.charCodeAt(n));)n++;m=n}else w=3;return e.substring(f,m)}l(A,"scanNumber");function b(){let f="",m=n;for(;;){if(n>=i){f+=e.substring(m,n),w=2;break}const g=e.charCodeAt(n);if(g===34){f+=e.substring(m,n),n++;break}if(g===92){if(f+=e.substring(m,n),n++,n>=i){w=2;break}switch(e.charCodeAt(n++)){case 34:f+='"';break;case 92:f+="\\";break;case 47:f+="/";break;case 98:f+="\b";break;case 102:f+="\f";break;case 110:f+=`
`;break;case 114:f+="\r";break;case 116:f+="	";break;case 117:const j=O(4,!0);j>=0?f+=String.fromCharCode(j):w=4;break;default:w=5}m=n;continue}if(g>=0&&g<=31)if(h(g)){f+=e.substring(m,n),w=2;break}else w=6;n++}return f}l(b,"scanString");function $(){if(s="",w=0,o=n,u=c,T=p,n>=i)return o=i,r=17;let f=e.charCodeAt(n);if(G(f)){do n++,s+=String.fromCharCode(f),f=e.charCodeAt(n);while(G(f));return r=15}if(h(f))return n++,s+=String.fromCharCode(f),f===13&&e.charCodeAt(n)===10&&(n++,s+=`
`),c++,p=n,r=14;switch(f){case 123:return n++,r=1;case 125:return n++,r=2;case 91:return n++,r=3;case 93:return n++,r=4;case 58:return n++,r=6;case 44:return n++,r=5;case 34:return n++,s=b(),r=10;case 47:const m=n-1;if(e.charCodeAt(n+1)===47){for(n+=2;n<i&&!h(e.charCodeAt(n));)n++;return s=e.substring(m,n),r=12}if(e.charCodeAt(n+1)===42){n+=2;const g=i-1;let y=!1;for(;n<g;){const j=e.charCodeAt(n);if(j===42&&e.charCodeAt(n+1)===47){n+=2,y=!0;break}n++,h(j)&&(j===13&&e.charCodeAt(n)===10&&n++,c++,p=n)}return y||(n++,w=1),s=e.substring(m,n),r=13}return s+=String.fromCharCode(f),n++,r=16;case 45:if(s+=String.fromCharCode(f),n++,n===i||!N(e.charCodeAt(n)))return r=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return s+=A(),r=11;default:for(;n<i&&U(f);)n++,f=e.charCodeAt(n);if(o!==n){switch(s=e.substring(o,n),s){case"true":return r=8;case"false":return r=9;case"null":return r=7}return r=16}return s+=String.fromCharCode(f),n++,r=16}}l($,"scanNext");function U(f){if(G(f)||h(f))return!1;switch(f){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}l(U,"isUnknownContentCharacter");function E(){let f;do f=$();while(f>=12&&f<=15);return f}return l(E,"scanNextNonTrivia"),{setPosition:v,getPosition:l(()=>n,"getPosition"),scan:t?E:$,getToken:l(()=>r,"getToken"),getTokenValue:l(()=>s,"getTokenValue"),getTokenOffset:l(()=>o,"getTokenOffset"),getTokenLength:l(()=>n-o,"getTokenLength"),getTokenStartLine:l(()=>u,"getTokenStartLine"),getTokenStartCharacter:l(()=>o-T,"getTokenStartCharacter"),getTokenError:l(()=>w,"getTokenError")}}l(_e,"createScanner");function G(e){return e===32||e===9}l(G,"isWhiteSpace");function h(e){return e===10||e===13}l(h,"isLineBreak");function N(e){return e>=48&&e<=57}l(N,"isDigit");var te;(function(e){e[e.lineFeed=10]="lineFeed",e[e.carriageReturn=13]="carriageReturn",e[e.space=32]="space",e[e._0=48]="_0",e[e._1=49]="_1",e[e._2=50]="_2",e[e._3=51]="_3",e[e._4=52]="_4",e[e._5=53]="_5",e[e._6=54]="_6",e[e._7=55]="_7",e[e._8=56]="_8",e[e._9=57]="_9",e[e.a=97]="a",e[e.b=98]="b",e[e.c=99]="c",e[e.d=100]="d",e[e.e=101]="e",e[e.f=102]="f",e[e.g=103]="g",e[e.h=104]="h",e[e.i=105]="i",e[e.j=106]="j",e[e.k=107]="k",e[e.l=108]="l",e[e.m=109]="m",e[e.n=110]="n",e[e.o=111]="o",e[e.p=112]="p",e[e.q=113]="q",e[e.r=114]="r",e[e.s=115]="s",e[e.t=116]="t",e[e.u=117]="u",e[e.v=118]="v",e[e.w=119]="w",e[e.x=120]="x",e[e.y=121]="y",e[e.z=122]="z",e[e.A=65]="A",e[e.B=66]="B",e[e.C=67]="C",e[e.D=68]="D",e[e.E=69]="E",e[e.F=70]="F",e[e.G=71]="G",e[e.H=72]="H",e[e.I=73]="I",e[e.J=74]="J",e[e.K=75]="K",e[e.L=76]="L",e[e.M=77]="M",e[e.N=78]="N",e[e.O=79]="O",e[e.P=80]="P",e[e.Q=81]="Q",e[e.R=82]="R",e[e.S=83]="S",e[e.T=84]="T",e[e.U=85]="U",e[e.V=86]="V",e[e.W=87]="W",e[e.X=88]="X",e[e.Y=89]="Y",e[e.Z=90]="Z",e[e.asterisk=42]="asterisk",e[e.backslash=92]="backslash",e[e.closeBrace=125]="closeBrace",e[e.closeBracket=93]="closeBracket",e[e.colon=58]="colon",e[e.comma=44]="comma",e[e.dot=46]="dot",e[e.doubleQuote=34]="doubleQuote",e[e.minus=45]="minus",e[e.openBrace=123]="openBrace",e[e.openBracket=91]="openBracket",e[e.plus=43]="plus",e[e.slash=47]="slash",e[e.formFeed=12]="formFeed",e[e.tab=9]="tab"})(te||(te={})),new Array(20).fill(0).map((e,t)=>" ".repeat(t));const D=200;new Array(D).fill(0).map((e,t)=>`
`+" ".repeat(t)),new Array(D).fill(0).map((e,t)=>"\r"+" ".repeat(t)),new Array(D).fill(0).map((e,t)=>`\r
`+" ".repeat(t)),new Array(D).fill(0).map((e,t)=>`
`+"	".repeat(t)),new Array(D).fill(0).map((e,t)=>"\r"+"	".repeat(t)),new Array(D).fill(0).map((e,t)=>`\r
`+"	".repeat(t));var I;(function(e){e.DEFAULT={allowTrailingComma:!1}})(I||(I={}));function $e(e,t=[],i=I.DEFAULT){let n=null,s=[];const o=[];function r(u){Array.isArray(s)?s.push(u):n!==null&&(s[n]=u)}return l(r,"onValue"),ye(e,{onObjectBegin:l(()=>{const u={};r(u),o.push(s),s=u,n=null},"onObjectBegin"),onObjectProperty:l(u=>{n=u},"onObjectProperty"),onObjectEnd:l(()=>{s=o.pop()},"onObjectEnd"),onArrayBegin:l(()=>{const u=[];r(u),o.push(s),s=u,n=null},"onArrayBegin"),onArrayEnd:l(()=>{s=o.pop()},"onArrayEnd"),onLiteralValue:r,onError:l((u,p,T)=>{t.push({error:u,offset:p,length:T})},"onError")},i),s[0]}l($e,"parse$1");function ye(e,t,i=I.DEFAULT){const n=_e(e,!1),s=[];function o(k){return k?()=>k(n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter()):()=>!0}l(o,"toNoArgVisit");function r(k){return k?()=>k(n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter(),()=>s.slice()):()=>!0}l(r,"toNoArgVisitWithPath");function c(k){return k?_=>k(_,n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter()):()=>!0}l(c,"toOneArgVisit");function u(k){return k?_=>k(_,n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter(),()=>s.slice()):()=>!0}l(u,"toOneArgVisitWithPath");const p=r(t.onObjectBegin),T=u(t.onObjectProperty),w=o(t.onObjectEnd),O=r(t.onArrayBegin),v=o(t.onArrayEnd),A=u(t.onLiteralValue),b=c(t.onSeparator),$=o(t.onComment),U=c(t.onError),E=i&&i.disallowComments,f=i&&i.allowTrailingComma;function m(){for(;;){const k=n.scan();switch(n.getTokenError()){case 4:g(14);break;case 5:g(15);break;case 3:g(13);break;case 1:E||g(11);break;case 2:g(12);break;case 6:g(16);break}switch(k){case 12:case 13:E?g(10):$();break;case 16:g(1);break;case 15:case 14:break;default:return k}}}l(m,"scanNext");function g(k,_=[],C=[]){if(U(k),_.length+C.length>0){let P=n.getToken();for(;P!==17;){if(_.indexOf(P)!==-1){m();break}else if(C.indexOf(P)!==-1)break;P=m()}}}l(g,"handleError");function y(k){const _=n.getTokenValue();return k?A(_):(T(_),s.push(_)),m(),!0}l(y,"parseString");function j(){switch(n.getToken()){case 11:const k=n.getTokenValue();let _=Number(k);isNaN(_)&&(g(2),_=0),A(_);break;case 7:A(null);break;case 8:A(!0);break;case 9:A(!1);break;default:return!1}return m(),!0}l(j,"parseLiteral");function ke(){return n.getToken()!==10?(g(3,[],[2,5]),!1):(y(!1),n.getToken()===6?(b(":"),m(),V()||g(4,[],[2,5])):g(5,[],[2,5]),s.pop(),!0)}l(ke,"parseProperty");function be(){p(),m();let k=!1;for(;n.getToken()!==2&&n.getToken()!==17;){if(n.getToken()===5){if(k||g(4,[],[]),b(","),m(),n.getToken()===2&&f)break}else k&&g(6,[],[]);ke()||g(4,[],[2,5]),k=!0}return w(),n.getToken()!==2?g(7,[2],[]):m(),!0}l(be,"parseObject");function we(){O(),m();let k=!0,_=!1;for(;n.getToken()!==4&&n.getToken()!==17;){if(n.getToken()===5){if(_||g(4,[],[]),b(","),m(),n.getToken()===4&&f)break}else _&&g(6,[],[]);k?(s.push(0),k=!1):s[s.length-1]++,V()||g(4,[],[4,5]),_=!0}return v(),k||s.pop(),n.getToken()!==4?g(8,[4],[]):m(),!0}l(we,"parseArray");function V(){switch(n.getToken()){case 3:return we();case 1:return be();case 10:return y(!0);default:return j()}}return l(V,"parseValue"),m(),n.getToken()===17?i.allowEmptyContent?!0:(g(4,[],[]),!1):V()?(n.getToken()!==17&&g(9,[],[]),!0):(g(4,[],[]),!1)}l(ye,"visit");var ie;(function(e){e[e.None=0]="None",e[e.UnexpectedEndOfComment=1]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=2]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=3]="UnexpectedEndOfNumber",e[e.InvalidUnicode=4]="InvalidUnicode",e[e.InvalidEscapeCharacter=5]="InvalidEscapeCharacter",e[e.InvalidCharacter=6]="InvalidCharacter"})(ie||(ie={}));var se;(function(e){e[e.OpenBraceToken=1]="OpenBraceToken",e[e.CloseBraceToken=2]="CloseBraceToken",e[e.OpenBracketToken=3]="OpenBracketToken",e[e.CloseBracketToken=4]="CloseBracketToken",e[e.CommaToken=5]="CommaToken",e[e.ColonToken=6]="ColonToken",e[e.NullKeyword=7]="NullKeyword",e[e.TrueKeyword=8]="TrueKeyword",e[e.FalseKeyword=9]="FalseKeyword",e[e.StringLiteral=10]="StringLiteral",e[e.NumericLiteral=11]="NumericLiteral",e[e.LineCommentTrivia=12]="LineCommentTrivia",e[e.BlockCommentTrivia=13]="BlockCommentTrivia",e[e.LineBreakTrivia=14]="LineBreakTrivia",e[e.Trivia=15]="Trivia",e[e.Unknown=16]="Unknown",e[e.EOF=17]="EOF"})(se||(se={}));const Be=$e;var oe;(function(e){e[e.InvalidSymbol=1]="InvalidSymbol",e[e.InvalidNumberFormat=2]="InvalidNumberFormat",e[e.PropertyNameExpected=3]="PropertyNameExpected",e[e.ValueExpected=4]="ValueExpected",e[e.ColonExpected=5]="ColonExpected",e[e.CommaExpected=6]="CommaExpected",e[e.CloseBraceExpected=7]="CloseBraceExpected",e[e.CloseBracketExpected=8]="CloseBracketExpected",e[e.EndOfFileExpected=9]="EndOfFileExpected",e[e.InvalidCommentToken=10]="InvalidCommentToken",e[e.UnexpectedEndOfComment=11]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=12]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=13]="UnexpectedEndOfNumber",e[e.InvalidUnicode=14]="InvalidUnicode",e[e.InvalidEscapeCharacter=15]="InvalidEscapeCharacter",e[e.InvalidCharacter=16]="InvalidCharacter"})(oe||(oe={}));const le=l((e,t)=>Be(je(t,e,"utf8")),"readJsonc"),z=Symbol("implicitBaseUrl"),L="${configDir}",Fe=l(()=>{const{findPnpApi:e}=Te;return e&&e(process.cwd())},"getPnpApi"),Q=l((e,t,i,n)=>{const s=`resolveFromPackageJsonPath:${e}:${t}:${i}`;if(n!=null&&n.has(s))return n.get(s);const o=le(e,n);if(!o)return;let r=t||"tsconfig.json";if(!i&&o.exports)try{const[c]=Ae.resolveExports(o.exports,t,["require","types"]);r=c}catch{return!1}else!t&&o.tsconfig&&(r=o.tsconfig);return r=a.join(e,"..",r),n==null||n.set(s,r),r},"resolveFromPackageJsonPath"),q="package.json",H="tsconfig.json",Le=l((e,t,i)=>{let n=e;if(e===".."&&(n=a.join(n,H)),e[0]==="."&&(n=a.resolve(t,n)),a.isAbsolute(n)){if(F(i,n)){if(d(i,n).isFile())return n}else if(!n.endsWith(".json")){const v=`${n}.json`;if(F(i,v))return v}return}const[s,...o]=e.split("/"),r=s[0]==="@"?`${s}/${o.shift()}`:s,c=o.join("/"),u=Fe();if(u){const{resolveRequest:v}=u;try{if(r===e){const A=v(a.join(r,q),t);if(A){const b=Q(A,c,!1,i);if(b&&F(i,b))return b}}else{let A;try{A=v(e,t,{extensions:[".json"]})}catch{A=v(a.join(e,H),t)}if(A)return A}}catch{}}const p=ne(a.resolve(t),a.join("node_modules",r),i);if(!p||!d(i,p).isDirectory())return;const T=a.join(p,q);if(F(i,T)){const v=Q(T,c,!1,i);if(v===!1)return;if(v&&F(i,v)&&d(i,v).isFile())return v}const w=a.join(p,c),O=w.endsWith(".json");if(!O){const v=`${w}.json`;if(F(i,v))return v}if(F(i,w)){if(d(i,w).isDirectory()){const v=a.join(w,q);if(F(i,v)){const b=Q(v,"",!0,i);if(b&&F(i,b))return b}const A=a.join(w,H);if(F(i,A))return A}else if(O)return w}},"resolveExtendsPath"),X=l((e,t)=>J(a.relative(e,t)),"pathRelative"),re=["files","include","exclude"],Ue=l((e,t,i,n)=>{const s=Le(e,t,n);if(!s)throw new Error(`File '${e}' not found.`);if(i.has(s))throw new Error(`Circularity detected while resolving configuration: ${s}`);i.add(s);const o=a.dirname(s),r=ue(s,n,i);delete r.references;const{compilerOptions:c}=r;if(c){const{baseUrl:u}=c;u&&!u.startsWith(L)&&(c.baseUrl=B(a.relative(t,a.join(o,u)))||"./");let{outDir:p}=c;p&&(p.startsWith(L)||(p=a.relative(t,a.join(o,p))),c.outDir=B(p)||"./")}for(const u of re){const p=r[u];p&&(r[u]=p.map(T=>T.startsWith(L)?T:B(a.relative(t,a.join(o,T)))))}return r},"resolveExtends"),Ee=["outDir","declarationDir"],ue=l((e,t,i=new Set)=>{let n;try{n=le(e,t)||{}}catch{throw new Error(`Cannot resolve tsconfig at path: ${e}`)}if(typeof n!="object")throw new SyntaxError(`Failed to parse tsconfig at: ${e}`);const s=a.dirname(e);if(n.compilerOptions){const{compilerOptions:o}=n;o.paths&&!o.baseUrl&&(o[z]=s)}if(n.extends){const o=Array.isArray(n.extends)?n.extends:[n.extends];delete n.extends;for(const r of o.reverse()){const c=Ue(r,s,new Set(i),t),u={...c,...n,compilerOptions:{...c.compilerOptions,...n.compilerOptions}};c.watchOptions&&(u.watchOptions={...c.watchOptions,...n.watchOptions}),n=u}}if(n.compilerOptions){const{compilerOptions:o}=n,r=["baseUrl","rootDir"];for(const c of r){const u=o[c];if(u&&!u.startsWith(L)){const p=a.resolve(s,u),T=X(s,p);o[c]=T}}for(const c of Ee){let u=o[c];u&&(Array.isArray(n.exclude)||(n.exclude=[]),n.exclude.includes(u)||n.exclude.push(u),u.startsWith(L)||(u=J(u)),o[c]=u)}}else n.compilerOptions={};if(n.include?(n.include=n.include.map(B),n.files&&delete n.files):n.files&&(n.files=n.files.map(o=>o.startsWith(L)?o:J(o))),n.watchOptions){const{watchOptions:o}=n;o.excludeDirectories&&(o.excludeDirectories=o.excludeDirectories.map(r=>B(a.resolve(s,r))))}return n},"_parseTsconfig"),S=l((e,t)=>{if(e.startsWith(L))return B(a.join(t,e.slice(L.length)))},"interpolateConfigDir"),Ne=["outDir","declarationDir","outFile","rootDir","baseUrl","tsBuildInfoFile"],ce=l((e,t=new Map)=>{const i=a.resolve(e),n=ue(i,t),s=a.dirname(i),{compilerOptions:o}=n;if(o){for(const c of Ne){const u=o[c];if(u){const p=S(u,s);o[c]=p?X(s,p):u}}for(const c of["rootDirs","typeRoots"]){const u=o[c];u&&(o[c]=u.map(p=>{const T=S(p,s);return T?X(s,T):p}))}const{paths:r}=o;if(r)for(const c of Object.keys(r))r[c]=r[c].map(u=>{var p;return(p=S(u,s))!=null?p:u})}for(const r of re){const c=n[r];c&&(n[r]=c.map(u=>{var p;return(p=S(u,s))!=null?p:u}))}return n},"parseTsconfig"),De=l((e=process.cwd(),t="tsconfig.json",i=new Map)=>{const n=ne(B(e),t,i);if(!n)return null;const s=ce(n,i);return{path:n,config:s}},"getTsconfig"),he=/\*/g,fe=l((e,t)=>{const i=e.match(he);if(i&&i.length>1)throw new Error(t)},"assertStarCount"),Pe=l(e=>{if(e.includes("*")){const[t,i]=e.split("*");return{prefix:t,suffix:i}}return e},"parsePattern"),de=l(({prefix:e,suffix:t},i)=>i.startsWith(e)&&i.endsWith(t),"isPatternMatch"),Ie=l((e,t,i)=>Object.entries(e).map(([n,s])=>(fe(n,`Pattern '${n}' can have at most one '*' character.`),{pattern:Pe(n),substitutions:s.map(o=>{if(fe(o,`Substitution '${o}' in pattern '${n}' can have at most one '*' character.`),!t&&!R.test(o))throw new Error("Non-relative paths are not allowed when 'baseUrl' is not set. Did you forget a leading './'?");return a.resolve(i,o)})})),"parsePaths"),Se=l(e=>{const{compilerOptions:t}=e.config;if(!t)return null;const{baseUrl:i,paths:n}=t;if(!i&&!n)return null;const s=z in t&&t[z],o=a.resolve(a.dirname(e.path),i||s||"."),r=n?Ie(n,i,o):[];return c=>{if(R.test(c))return[];const u=[];for(const O of r){if(O.pattern===c)return O.substitutions.map(B);typeof O.pattern!="string"&&u.push(O)}let p,T=-1;for(const O of u)de(O.pattern,c)&&O.pattern.prefix.length>T&&(T=O.pattern.prefix.length,p=O);if(!p)return i?[B(a.join(o,c))]:[];const w=c.slice(p.pattern.prefix.length,c.length-p.pattern.suffix.length);return p.substitutions.map(O=>B(O.replace("*",w)))}},"createPathsMatcher"),pe=l(e=>{let t="";for(let i=0;i<e.length;i+=1){const n=e[i],s=n.toUpperCase();t+=n===s?n.toLowerCase():s}return t},"s"),xe=65,We=97,Ve=l(()=>Math.floor(Math.random()*26),"m"),Me=l(e=>Array.from({length:e},()=>String.fromCodePoint(Ve()+(Math.random()>.5?xe:We))).join(""),"S"),Re=l((e=Oe)=>{const t=process.execPath;if(e.existsSync(t))return!e.existsSync(pe(t));const i=`/${Me(10)}`;e.writeFileSync(i,"");const n=!e.existsSync(pe(i));return e.unlinkSync(i),n},"l"),{join:x}=a.posix,Y={ts:[".ts",".tsx",".d.ts"],cts:[".cts",".d.cts"],mts:[".mts",".d.mts"]},Je=l(e=>{const t=[...Y.ts],i=[...Y.cts],n=[...Y.mts];return e!=null&&e.allowJs&&(t.push(".js",".jsx"),i.push(".cjs"),n.push(".mjs")),[...t,...i,...n]},"getSupportedExtensions"),Ge=l(e=>{const t=[];if(!e)return t;const{outDir:i,declarationDir:n}=e;return i&&t.push(i),n&&t.push(n),t},"getDefaultExcludeSpec"),ae=l(e=>e.replaceAll(/[.*+?^${}()|[\]\\]/g,String.raw`\$&`),"escapeForRegexp"),ze=["node_modules","bower_components","jspm_packages"],Z=`(?!(${ze.join("|")})(/|$))`,Qe=/(?:^|\/)[^.*?]+$/,ge="**/*",W="[^/]",K="[^./]",me=process.platform==="win32",qe=l(({config:e,path:t},i=Re())=>{if("extends"in e)throw new Error("tsconfig#extends must be resolved. Use getTsconfig or parseTsconfig to resolve it.");if(!a.isAbsolute(t))throw new Error("The tsconfig path must be absolute");me&&(t=B(t));const n=a.dirname(t),{files:s,include:o,exclude:r,compilerOptions:c}=e,u=s==null?void 0:s.map(b=>x(n,b)),p=Je(c),T=i?"":"i",O=(r||Ge(c)).map(b=>{const $=x(n,b),U=ae($).replaceAll(String.raw`\*\*/`,"(.+/)?").replaceAll(String.raw`\*`,`${W}*`).replaceAll(String.raw`\?`,W);return new RegExp(`^${U}($|/)`,T)}),v=s||o?o:[ge],A=v?v.map(b=>{let $=x(n,b);Qe.test($)&&($=x($,ge));const U=ae($).replaceAll(String.raw`/\*\*`,`(/${Z}${K}${W}*)*?`).replaceAll(/(\/)?\\\*/g,(E,f)=>{const m=`(${K}|(\\.(?!min\\.js$))?)*`;return f?`/${Z}${K}${m}`:m}).replaceAll(/(\/)?\\\?/g,(E,f)=>{const m=W;return f?`/${Z}${m}`:m});return new RegExp(`^${U}$`,T)}):void 0;return b=>{if(!a.isAbsolute(b))throw new Error("filePath must be absolute");if(me&&(b=B(b)),u!=null&&u.includes(b))return e;if(!(!p.some($=>b.endsWith($))||O.some($=>$.test(b)))&&A&&A.some($=>$.test(b)))return e}},"createFilesMatcher");exports.createFilesMatcher=qe,exports.createPathsMatcher=Se,exports.getTsconfig=De,exports.parseTsconfig=ce;
