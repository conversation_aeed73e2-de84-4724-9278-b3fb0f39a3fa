{"name": "p-queue", "version": "6.6.2", "description": "Promise queue with concurrency control", "license": "MIT", "repository": "sindresorhus/p-queue", "funding": "https://github.com/sponsors/sindresorhus", "main": "dist/index.js", "engines": {"node": ">=8"}, "scripts": {"build": "del dist && tsc", "test": "xo && npm run build && nyc ava", "bench": "ts-node bench.ts", "prepublishOnly": "npm run build"}, "files": ["dist"], "types": "dist/index.d.ts", "keywords": ["promise", "queue", "enqueue", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "priority", "priorityqueue", "fifo", "job", "task", "async", "await", "promises", "bluebird"], "dependencies": {"eventemitter3": "^4.0.4", "p-timeout": "^3.2.0"}, "devDependencies": {"@sindresorhus/tsconfig": "^0.7.0", "@types/benchmark": "^1.0.33", "@types/node": "^14.6.0", "ava": "^2.0.0", "benchmark": "^2.1.4", "codecov": "^3.7.2", "del-cli": "^3.0.1", "delay": "^4.4.0", "in-range": "^2.0.0", "nyc": "^15.1.0", "random-int": "^2.0.1", "time-span": "^4.0.0", "ts-node": "^9.0.0", "typescript": "^4.0.2", "xo": "^0.33.0"}, "ava": {"babel": false, "compileEnhancements": false, "extensions": ["ts"], "require": ["ts-node/register"], "files": ["test/**"]}, "xo": {"rules": {"@typescript-eslint/member-ordering": "off", "node/no-unsupported-features/es-syntax": "off", "@typescript-eslint/no-floating-promises": "off", "import/no-named-default": "off", "@typescript-eslint/no-invalid-void-type": "off"}}, "nyc": {"extension": [".ts"]}}