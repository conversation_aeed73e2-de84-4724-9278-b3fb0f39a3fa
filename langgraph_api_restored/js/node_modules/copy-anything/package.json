{"name": "copy-anything", "version": "3.0.5", "description": "An optimised way to copy'ing an object. A small and simple integration", "type": "module", "sideEffects": false, "types": "./dist/index.d.ts", "module": "./dist/index.js", "main": "./dist/index.js", "exports": {".": {"require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}, "import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "files": ["dist"], "engines": {"node": ">=12.13"}, "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src --ext .ts", "build": "rollup -c ./rollup.config.js", "release": "npm run lint && del dist && npm run build && np"}, "dependencies": {"is-what": "^4.1.8"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "del-cli": "^5.0.0", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-tree-shaking": "^1.10.0", "np": "^7.7.0", "prettier": "^2.8.8", "rollup": "^3.23.0", "rollup-plugin-dts": "^5.3.0", "rollup-plugin-esbuild": "^5.0.0", "typescript": "^4.9.5", "vitest": "^0.31.0"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": "<PERSON>", "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "np": {"yarn": false, "branch": "production"}, "eslintConfig": {"ignorePatterns": ["node_modules", "dist", "scripts", "test"], "root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "tree-shaking"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-ignore": "off", "tree-shaking/no-side-effects-in-initialization": "error", "@typescript-eslint/ban-ts-comment": "off"}}}