# Source Generated with <PERSON>om<PERSON>le++
# File: asyncio.pyc (Python 3.12)

import asyncio
import concurrent.futures as concurrent
from collections.abc import <PERSON><PERSON><PERSON><PERSON><PERSON>, Coroutine
from contextlib import AbstractAsyncContextManager, suppress
from functools import partial
from typing import Any, Generic, TypeVar
import structlog
T = TypeVar('T')
logger = structlog.stdlib.get_logger(__name__)
_MAIN_LOOP: asyncio.AbstractEventLoop | None = None

def set_event_loop(loop = None):
    global _MAIN_LOOP
    _MAIN_LOOP = loop


def get_event_loop():
    pass
# WARNING: Decompyle incomplete


async def sleep_if_not_done(delay = None, done = None):
    pass
# WARNING: Decompyle incomplete


class ValueEvent(asyncio.Event):
    
    def set(self = None, value = None):
        '''Set the internal flag to true. All coroutines waiting for it to
        become set are awakened. Coroutine that call wait() once the flag is
        true will not block at all.
        '''
        if not self._value:
            self._value = value
            for fut in self._waiters:
                if fut.done():
                    continue
                fut.set_result(value)
            return None

    
    async def wait(self):
        '''Block until the internal flag is set.

        If the internal flag is set on entry, return value
        immediately.  Otherwise, block until another coroutine calls
        set() to set the flag, then return the value.
        '''
        pass
    # WARNING: Decompyle incomplete



async def wait_if_not_done(coro = None, done = None):
    '''Wait for the coroutine to finish or the event to be set.'''
    pass
# WARNING: Decompyle incomplete

PENDING_TASKS = set()

def _create_task_done_callback(ignore_exceptions = None, task = None):
    PENDING_TASKS.discard(task)
    exc = task.exception()
    if task.exception():
        if not isinstance(exc, ignore_exceptions):
            logger.exception('asyncio.task failed', exc_info = exc)
            return None
        return None
    return None
# WARNING: Decompyle incomplete


def create_task(coro = None, ignore_exceptions = None):
    '''Create a new task in the current task group and return it.'''
    task = asyncio.create_task(coro)
    PENDING_TASKS.add(task)
    task.add_done_callback(partial(_create_task_done_callback, ignore_exceptions))
    return task


def run_coroutine_threadsafe(coro = None, ignore_exceptions = None):
    pass
# WARNING: Decompyle incomplete


def call_soon_in_main_loop(coro = None):
    '''Run a coroutine in the main event loop.'''
    lg_future = future
    import langgraph_api.utils
# WARNING: Decompyle incomplete


def call_soon_threadsafe(callback = None, *args):
    '''Run a coroutine in the main event loop.'''
    pass
# WARNING: Decompyle incomplete


def SimpleTaskGroup():
    '''SimpleTaskGroup'''
    tasks: set[asyncio.Task] = 'An async task group that can be configured to wait and/or cancel tasks on exit.\n\n    asyncio.TaskGroup and anyio.TaskGroup both expect enter and exit to be called\n    in the same asyncio task, which is not true for our use case, where exit is\n    shielded from cancellation.'
    
    def __init__(self = None, *, cancel, wait, taskset, taskgroup_name, *coros):
        pass
    # WARNING: Decompyle incomplete

    
    def _create_task_done_callback(self = None, ignore_exceptions = None, task = None):
        pass
    # WARNING: Decompyle incomplete

    
    def create_task(self = None, coro = None, ignore_exceptions = None):
        '''Create a new task in the current task group and return it.'''
        task = asyncio.create_task(coro)
        self.tasks.add(task)
        task.add_done_callback(partial(self._create_task_done_callback, ignore_exceptions))
        return task

    
    async def __aexit__(self = None, exc_type = None, exc_value = None, traceback = ('return', None)):
        pass
    # WARNING: Decompyle incomplete


SimpleTaskGroup = # FIXME: Unsupported node type 27(SimpleTaskGroup, 'SimpleTaskGroup', AbstractAsyncContextManager['SimpleTaskGroup'])

def to_aiter(*args):
    pass
# WARNING: Decompyle incomplete

V = TypeVar('V')

def aclosing():
    '''aclosing'''
    __doc__ = 'Async context manager for safely finalizing an asynchronously cleaned-up\n    resource such as an async generator, calling its ``aclose()`` method.\n\n    Code like this:\n\n        async with aclosing(<module>.fetch(<arguments>)) as agen:\n            <block>\n\n    is equivalent to this:\n\n        agen = <module>.fetch(<arguments>)\n        try:\n            <block>\n        finally:\n            await agen.aclose()\n\n    '
    
    def __init__(self = None, thing = None):
        self.thing = thing

    
    async def __aenter__(self = None):
        pass
    # WARNING: Decompyle incomplete

    
    async def __aexit__(self, *exc_info):
        pass
    # WARNING: Decompyle incomplete


aclosing = # FIXME: Unsupported node type 27(aclosing, 'aclosing', Generic[V], AbstractAsyncContextManager[V])

def aclosing_aiter(aiterator = None):
    pass
# WARNING: Decompyle incomplete


def AsyncQueue():
    '''AsyncQueue'''
    __doc__ = 'Async unbounded FIFO queue with a wait() method.\n\n    Subclassed from asyncio.Queue, adding a wait() method.'
    
    async def wait(self = None):
        """If queue is empty, wait until an item is available.

        Copied from Queue.get(), removing the call to .get_nowait(),
        ie. this doesn't consume the item, just waits for it.
        """
        pass
    # WARNING: Decompyle incomplete


AsyncQueue = # FIXME: Unsupported node type 27(AsyncQueue, 'AsyncQueue', Generic[T], asyncio.Queue[T])
