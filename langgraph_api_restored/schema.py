# Source Generated with Decompyle++
# File: schema.pyc (Python 3.12)

from collections.abc import Sequence
from datetime import datetime
from typing import Any, Literal, NotRequired, Optional, TypeAlias
from uuid import UUID
from langchain_core.runnables.config import RunnableConfig
from typing_extensions import TypedDict
from langgraph_api.serde import Fragment
MetadataInput = dict[(str, Any)] | None
MetadataValue = dict[(str, Any)]
RunStatus = Literal[('pending', 'running', 'error', 'success', 'timeout', 'interrupted')]
ThreadStatus = Literal[('idle', 'busy', 'interrupted', 'error')]
StreamMode = Literal[('values', 'messages', 'updates', 'events', 'debug', 'tasks', 'checkpoints', 'custom')]
MultitaskStrategy = Literal[('reject', 'rollback', 'interrupt', 'enqueue')]
OnConflictBehavior = Literal[('raise', 'do_nothing')]
OnCompletion = Literal[('delete', 'keep')]
IfNotExists = Literal[('create', 'reject')]
All = Literal['*']
Context: TypeAlias = dict[(str, Any)]

def Config():
    '''Config'''
    configurable: dict[(str, Any)] = 'Config'

Config = Config = TypedDict(\'Config\', {}, total=False)

class Checkpoint(TypedDict):
    checkpoint_map: dict[(str, Any)] | None = 'Checkpoint'


class Assistant(TypedDict):
    version: int = 'Assistant model.'


class Interrupt(TypedDict):
    value: Any = None  # Interrupt


def DeprecatedInterrupt():
    '''DeprecatedInterrupt'''
    when: Literal['during'] = 'We document this old interrupt format internally, but not in API spec.\n\n    Should be dropped with lg-api v1.0.0.\n    '

DeprecatedInterrupt = DeprecatedInterrupt = TypedDict(\'DeprecatedInterrupt\', {}, total=False)

class Thread(TypedDict):
    interrupts: dict[(str, list[Interrupt])] = 'Thread'


class ThreadTask(TypedDict):
    state: Optional['ThreadState'] = 'ThreadTask'


class ThreadState(TypedDict):
    interrupts: list[Interrupt] = 'ThreadState'


class RunKwargs(TypedDict):
    checkpoint_during: bool = None  # RunKwargs


class Run(TypedDict):
    multitask_strategy: MultitaskStrategy = None  # Run


class RunSend(TypedDict):
    input: dict[(str, Any)] | None = 'RunSend'


class RunCommand(TypedDict):
    resume: Any | None = None  # RunCommand


class Cron(TypedDict):
    now: NotRequired[datetime] = 'Cron model.'


class ThreadUpdateResponse(TypedDict):
    checkpoint: Checkpoint = 'Response for updating a thread.'


class QueueStats(TypedDict):
    med_age_secs: datetime | None = None  # QueueStats

AssistantSelectField = Literal[('assistant_id', 'graph_id', 'name', 'description', 'config', 'context', 'created_at', 'updated_at', 'metadata', 'version')]
ASSISTANT_FIELDS: set[str] = set(AssistantSelectField.__args__)
ThreadSelectField = Literal[('thread_id', 'created_at', 'updated_at', 'metadata', 'config', 'context', 'status', 'values', 'interrupts')]
THREAD_FIELDS: set[str] = set(ThreadSelectField.__args__)
RunSelectField = Literal[('run_id', 'thread_id', 'assistant_id', 'created_at', 'updated_at', 'status', 'metadata', 'kwargs', 'multitask_strategy')]
RUN_FIELDS: set[str] = set(RunSelectField.__args__)
CronSelectField = Literal[('cron_id', 'assistant_id', 'thread_id', 'end_time', 'schedule', 'created_at', 'updated_at', 'user_id', 'payload', 'next_run_date', 'metadata', 'now')]
CRON_FIELDS: set[str] = set(CronSelectField.__args__)
