# Source Generated with <PERSON>om<PERSON>le++
# File: server.pyc (Python 3.12)

import langgraph_api.patch
import sys
import os
import logging
import typing
disable_truststore = os.getenv('DISABLE_TRUSTSTORE')
if not os.getenv('DISABLE_TRUSTSTORE') or disable_truststore.lower() == 'true':
    import truststore
    truststore.inject_into_ssl()
from contextlib import asynccontextmanager
import jsonschema_rs
import structlog
from langgraph.errors import EmptyInputError, InvalidUpdateError
from langgraph_sdk.client import configure_loopback_transports
from starlette.applications import Starlette
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware
from starlette.routing import Mount
from starlette.types import Receive, Scope, Send
from langgraph_api.config import config
from langgraph_api.api import meta_routes, routes, user_router
from langgraph_api.api.openapi import set_custom_spec
from langgraph_api.errors import overloaded_error_handler, validation_error_handler, value_error_handler
from langgraph_api.js.base import is_js_path
from langgraph_api.middleware.http_logger import AccessLoggerMiddleware
from langgraph_api.middleware.private_network import PrivateNetworkMiddleware
from langgraph_api.middleware.request_id import RequestIdMiddleware
from langgraph_api.utils import SchemaGenerator
# from langgraph_runtime.lifespan import lifespan  # FIXME: Module not found
# from langgraph_runtime.retry import OVERLOADED_EXCEPTIONS  # FIXME: Module not found
logging.captureWarnings(True)
logger = structlog.stdlib.get_logger(__name__)
middleware = []
if config.ALLOW_PRIVATE_NETWORK:
    middleware.append(Middleware(PrivateNetworkMiddleware))
if config.HTTP_CONFIG:
    app = config.HTTP_CONFIG.get('app')
    if config.HTTP_CONFIG.get('app') and is_js_path(app.split(':')[0]):
        from langgraph_api.js.remote import JSCustomHTTPProxyMiddleware
        middleware.append(Middleware(JSCustomHTTPProxyMiddleware))
# WARNING: Decompyle incomplete
