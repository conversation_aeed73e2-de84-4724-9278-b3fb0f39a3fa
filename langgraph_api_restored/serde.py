# Source Generated with <PERSON>om<PERSON>le++
# File: serde.pyc (Python 3.12)

import asyncio
import base64
import re
import uuid
from base64 import b64encode
from collections import deque
from collections.abc import Mapping
from datetime import timedel<PERSON>, timezone
from decimal import Decimal
from ipaddress import IPv4Address, IPv4Interface, IPv4Network, IPv6Address, IPv6Interface, IPv6Network
from pathlib import Path
from re import Pattern
from typing import Any, NamedTuple, cast
from zoneinfo import ZoneInfo
import cloudpickle
import orjson
import structlog
from langgraph.checkpoint.serde.jsonplus import JsonPlusSerializer
logger = structlog.stdlib.get_logger(__name__)

class Fragment(NamedTuple):
    buf: bytes = None  # Fragment


def decimal_encoder(dec_value = None):
    '''
    Encodes a Decimal as int of there\'s no exponent, otherwise float

    This is useful when we use ConstrainedDecimal to represent Numeric(x,0)
    where a integer (but not int typed) is used. Encoding this as a float
    results in failed round-tripping between encode and parse.
    Our Id type is a prime example of this.

    >>> decimal_encoder(Decimal("1.0"))
    1.0

    >>> decimal_encoder(Decimal("1"))
    1
    '''
    if dec_value.is_finite() or cast(int, dec_value.as_tuple().exponent) < 0:
        return float(dec_value)
    return None(dec_value)


def default(obj):
    if isinstance(obj, Fragment):
        return orjson.Fragment(obj.buf)
    if not None(obj, 'model_dump') and callable(obj.model_dump) and isinstance(obj, type):
        return obj.model_dump()
    if not None(obj, 'dict') and callable(obj.dict) and isinstance(obj, type):
        return obj.dict()
    if not None(obj, '_asdict') and callable(obj._asdict) and isinstance(obj, type):
        return obj._asdict()
    if None(obj, BaseException):
        return {
            'error': type(obj).__name__,
            'message': str(obj) }
    if None(obj, (set, frozenset, deque)):
        return list(obj)
    if None(obj, (timezone, ZoneInfo)):
        return obj.tzname(None)
    if None(obj, timedelta):
        return obj.total_seconds()
    if None(obj, Decimal):
        return decimal_encoder(obj)
    if None(obj, (uuid.UUID, IPv4Address, IPv4Interface, IPv4Network, IPv6Address, IPv6Interface, IPv6Network, Path)):
        return str(obj)
    if None(obj, Pattern):
        return obj.pattern
    if None(obj, bytes | bytearray):
        return b64encode(obj).decode()

_option = orjson.OPT_SERIALIZE_NUMPY | orjson.OPT_NON_STR_KEYS
_SURROGATE_RE = re.compile('[\\ud800-\\udfff]')

def _strip_surr(s = None):
    pass
# WARNING: Decompyle incomplete


def _sanitise(o = None):
    if isinstance(o, str):
        return _strip_surr(o)
# WARNING: Decompyle incomplete


def json_dumpb(obj = None):
    dumped = orjson.dumps(obj, default = default, option = _option)
    return dumped.replace(b'\\\\u0000', b'').replace(b'\\u0000', b'')
# WARNING: Decompyle incomplete


def json_loads(content = None):
    if isinstance(content, Fragment):
        content = content.buf
    if isinstance(content, dict):
        return content
    return None.loads(cast(bytes, content))


async def ajson_loads(content = None):
    pass
# WARNING: Decompyle incomplete


class Serializer(JsonPlusSerializer):
    pass
# WARNING: Decompyle incomplete

mpack_keys = {
    'method',
    'value'}
SERIALIZER = Serializer()

async def reserialize_message(message = None):
    pass
# WARNING: Decompyle incomplete

