# Source Generated with Decom<PERSON>le++
# File: config.pyc (Python 3.12)

import os
from os import environ, getenv
from typing import Literal
import orjson
from starlette.config import Config, undefined
from starlette.datastructures import CommaSeparatedStrings
from typing_extensions import TypedDict
from langgraph_api import traceblock

def CorsConfig():
    '''CorsConfig'''
    max_age: int = None  # CorsConfig

CorsConfig = CorsConfig = TypedDict(\'CorsConfig\', {}, total=False)

class ConfigurableHeaders(TypedDict):
    excludes: list[str] | None = 'ConfigurableHeaders'


def HttpConfig():
    '''HttpConfig'''
    logging_headers: ConfigurableHeaders | None = None  # HttpConfig

HttpConfig = HttpConfig = TypedDict(\'HttpConfig\', {}, total=False)

def ThreadTTLConfig():
    '''ThreadTTLConfig'''
    sweep_interval_minutes: int | None = None  # ThreadTTLConfig

ThreadTTLConfig = ThreadTTLConfig = TypedDict(\'ThreadTTLConfig\', {}, total=False)

def IndexConfig():
    '''IndexConfig'''
    fields: list[str] | None = 'Configuration for indexing documents for semantic search in the store.'

IndexConfig = IndexConfig = TypedDict(\'IndexConfig\', {}, total=False)

def TTLConfig():
    '''TTLConfig'''
    sweep_interval_minutes: int | None = 'Configuration for TTL (time-to-live) behavior in the store.'

TTLConfig = TTLConfig = TypedDict(\'TTLConfig\', {}, total=False)

def StoreConfig():
    '''StoreConfig'''
    ttl: TTLConfig = None  # StoreConfig

StoreConfig = StoreConfig = TypedDict(\'StoreConfig\', {}, total=False)

def CheckpointerConfig():
    '''CheckpointerConfig'''
    ttl: ThreadTTLConfig | None = 'Configuration for the built-in checkpointer, which handles checkpointing of state.\n\n    If omitted, no checkpointer is set up (the object store will still be present, however).\n    '

CheckpointerConfig = CheckpointerConfig = TypedDict(\'CheckpointerConfig\', {}, total=False)
env = Config()

def _parse_json(json = None):
    if not json:
        return None
    parsed = orjson.loads(json)
    if not parsed:
        return None
    return parsed

STATS_INTERVAL_SECS = env('STATS_INTERVAL_SECS', cast = int, default = 60)
DATABASE_URI = env('DATABASE_URI', cast = str, default = getenv('POSTGRES_URI', undefined))
MIGRATIONS_PATH = env('MIGRATIONS_PATH', cast = str, default = '/storage/migrations')
POSTGRES_POOL_MAX_SIZE = env('LANGGRAPH_POSTGRES_POOL_MAX_SIZE', cast = int, default = 150)
RESUMABLE_STREAM_TTL_SECONDS = env('RESUMABLE_STREAM_TTL_SECONDS', cast = int, default = 120)

def _get_encryption_key(key_str = None):
    if not key_str:
        return None
    key = key_str.encode(encoding = 'utf-8')
    if len(key) not in (16, 24, 32):
        raise ValueError('LANGGRAPH_AES_KEY must be 16, 24, or 32 bytes long.')
    return key

LANGGRAPH_AES_KEY = env('LANGGRAPH_AES_KEY', default = None, cast = _get_encryption_key)
REDIS_URI = env('REDIS_URI', cast = str)
REDIS_CLUSTER = env('REDIS_CLUSTER', cast = bool, default = False)
REDIS_MAX_CONNECTIONS = env('REDIS_MAX_CONNECTIONS', cast = int, default = 2000)
REDIS_CONNECT_TIMEOUT = env('REDIS_CONNECT_TIMEOUT', cast = float, default = 10)
REDIS_MAX_IDLE_TIME = env('REDIS_MAX_IDLE_TIME', cast = float, default = 120)
REDIS_STREAM_TIMEOUT = env('REDIS_STREAM_TIMEOUT', cast = float, default = 30)
REDIS_KEY_PREFIX = env('REDIS_KEY_PREFIX', cast = str, default = '')
RUN_STATS_CACHE_SECONDS = env('RUN_STATS_CACHE_SECONDS', cast = int, default = 60)
ALLOW_PRIVATE_NETWORK = env('ALLOW_PRIVATE_NETWORK', cast = bool, default = False)
HTTP_CONFIG: HttpConfig | None = env('LANGGRAPH_HTTP', cast = _parse_json, default = None)
STORE_CONFIG: StoreConfig | None = env('LANGGRAPH_STORE', cast = _parse_json, default = None)
if not env('MOUNT_PREFIX', cast = str, default = None):
    env('MOUNT_PREFIX', cast = str, default = None)
MOUNT_PREFIX: str | None = HTTP_CONFIG.get('mount_prefix') if HTTP_CONFIG else None
CORS_ALLOW_ORIGINS = env('CORS_ALLOW_ORIGINS', cast = CommaSeparatedStrings, default = '*')
if not env('CORS_CONFIG', cast = _parse_json, default = None):
    env('CORS_CONFIG', cast = _parse_json, default = None)
CORS_CONFIG: CorsConfig | None = HTTP_CONFIG.get('cors') if HTTP_CONFIG else None
# WARNING: Decompyle incomplete
