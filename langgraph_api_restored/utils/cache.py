# Source Generated with <PERSON>om<PERSON>le++
# File: cache.pyc (Python 3.12)

import asyncio
import time
from collections import OrderedDict
from typing import Generic, TypeVar
T = TypeVar('T')

def LRUCache():
    '''LRUCache'''
    __doc__ = 'LRU cache with TTL support.'
    
    def __init__(self = None, max_size = None, ttl = None):
        self._cache = OrderedDict()
        self._max_size = max_size if max_size > 0 else 1000
        self._ttl = ttl

    
    def _get_time(self = None):
        '''Get current time, using loop.time() if available for better performance.'''
        return asyncio.get_event_loop().time()
    # WARNING: Decompyle incomplete

    
    def get(self = None, key = None):
        '''Get item from cache, returning None if expired or not found.'''
        if key not in self._cache:
            return None
        (value, timestamp) = self._cache[key]
        if self._get_time() - timestamp >= self._ttl:
            del self._cache[key]
            return None
        self._cache.move_to_end(key)
        return value

    
    def set(self = None, key = None, value = None):
        '''Set item in cache, evicting old entries if needed.'''
        if key in self._cache:
            del self._cache[key]
        if len(self._cache) >= self._max_size:
            self._cache.popitem(last = False)
            if len(self._cache) >= self._max_size:
                continue
        self._cache[key] = (value, self._get_time())

    
    def size(self = None):
        '''Return current cache size.'''
        return len(self._cache)

    
    def clear(self = None):
        '''Clear all entries from cache.'''
        self._cache.clear()


LRUCache = # FIXME: Unsupported node type 27(LRUCache, 'LRUCache', Generic[T])
