# Source Generated with <PERSON>ompyle++
# File: cli.pyc (Python 3.12)

import contextlib
import json
import logging
import os
import pathlib
import threading
import typing
from collections.abc import Mapping, Sequence
from typing import Literal
from typing_extensions import TypedDict
if typing.TYPE_CHECKING:
    from langgraph_api.config import HttpConfig, StoreConfig
logging.basicConfig(level = logging.INFO)
logger = logging.getLogger(__name__)

def _get_ls_origin():
    Client = Client
    import langsmith.client
    tracing_is_enabled = tracing_is_enabled
    import langsmith.utils
    if not tracing_is_enabled():
        return None
    client = Client()
    return client._host_url


def _get_org_id():
    Client = Client
    import langsmith.client
    tracing_is_enabled = tracing_is_enabled
    import langsmith.utils
    if not tracing_is_enabled():
        return None
    client = Client()
    response = client.request_with_retries('GET', '/api/v1/sessions', params = {
        'limit': 1 })
    result = response.json()
    if result:
        return result[0]['tenant_id']
# WARNING: Decompyle incomplete

patch_environment = (lambda : pass# WARNING: Decompyle incomplete
)()

def SecurityConfig():
    '''SecurityConfig'''
    paths: dict[(str, dict[(str, list)])] = 'SecurityConfig'

SecurityConfig = SecurityConfig = TypedDict(\'SecurityConfig\', {}, total=False)

def CacheConfig():
    '''CacheConfig'''
    max_size: int = None  # CacheConfig

CacheConfig = CacheConfig = TypedDict(\'CacheConfig\', {}, total=False)

def AuthConfig():
    '''AuthConfig'''
    cache: CacheConfig | None = None  # AuthConfig

AuthConfig = AuthConfig = TypedDict(\'AuthConfig\', {}, total=False)

def _check_newer_version(pkg = None, timeout = None):
    '''Log a notice if PyPI reports a newer version.'''
    md = metadata
    import importlib.metadata
    import json
    import urllib.request as urllib
    Version = Version
    import packaging.version
    thread_logger = logging.getLogger('check_version')
    if not thread_logger.handlers:
        handler = logging.StreamHandler()
        handler.setFormatter(logging.Formatter('%(message)s'))
        thread_logger.addHandler(handler)
    current = Version(md.version(pkg))
# WARNING: Decompyle incomplete


def run_server(host, port, reload, graphs, n_jobs_per_worker, env_file, open_browser, tunnel, debug_port, wait_for_client, env, reload_includes, reload_excludes, store, auth, http, ui, ui_config, studio_url = None, disable_persistence = None, allow_blocking = None, runtime_edition = ('127.0.0.1', 2024, False, None, None, None, False, False, None, False, None, None, None, None, None, None, None, None, None, False, False, 'inmem', 'WARNING'), server_level = ('host', str, 'port', int, 'reload', bool, 'graphs', dict | None, 'n_jobs_per_worker', int | None, 'env_file', str | None, 'open_browser', bool, 'tunnel', bool, 'debug_port', int | None, 'wait_for_client', bool, 'env', str | pathlib.Path | Mapping[(str, str)] | None, 'reload_includes', Sequence[str] | None, 'reload_excludes', Sequence[str] | None, 'store', typing.Optional['StoreConfig'], 'auth', AuthConfig | None, 'http', typing.Optional['HttpConfig'], 'ui', dict | None, 'ui_config', dict | None, 'studio_url', str | None, 'disable_persistence', bool, 'allow_blocking', bool, 'runtime_edition', Literal[('inmem', 'community', 'postgres')], 'server_level', str, 'kwargs', typing.Any), **kwargs):
    '''Run the LangGraph API server.'''
    pass
# WARNING: Decompyle incomplete


def main():
    import argparse
    parser = argparse.ArgumentParser(description = 'CLI entrypoint for running the LangGraph API server.')
    parser.add_argument('--host', default = '127.0.0.1', help = 'Host to bind the server to')
    parser.add_argument('--port', type = int, default = 2024, help = 'Port to bind the server to')
    parser.add_argument('--no-reload', action = 'store_true', help = 'Disable auto-reload')
    parser.add_argument('--config', default = 'langgraph.json', help = 'Path to configuration file')
    parser.add_argument('--n-jobs-per-worker', type = int, help = 'Number of jobs per worker. Default is None (meaning 10)')
    parser.add_argument('--no-browser', action = 'store_true', help = 'Disable automatic browser opening')
    parser.add_argument('--debug-port', type = int, help = 'Port for debugger to listen on (default: none)')
    parser.add_argument('--wait-for-client', action = 'store_true', help = 'Whether to break and wait for a debugger to attach')
    parser.add_argument('--tunnel', action = 'store_true', help = 'Expose the server via Cloudflare Tunnel')
    args = parser.parse_args()
# WARNING: Decompyle incomplete

if __name__ == '__main__':
    main()
    return None
